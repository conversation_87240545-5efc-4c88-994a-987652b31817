<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="initial-scale=1, width=device-width">
    <title>Componente - <PERSON><PERSON><PERSON></title>
    <link rel="stylesheet" href="./index.css" />
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Outfit:wght@100;300;400;500;600;700;800;900&display=swap" />
</head>
<body>
    <div class="frame-parent">
        <div class="component-container">
            <h1>Página en Construcción</h1>
            <p>Esta página está siendo desarrollada. Pronto estará disponible con más contenido.</p>
            <div class="back-button">
                <a href="./index.html">Volver al Inicio</a>
            </div>
        </div>
    </div>
    
    <style>
        .component-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            text-align: center;
            padding: 20px;
            font-family: 'Outfit', sans-serif;
        }
        
        .component-container h1 {
            color: #ffe4ac;
            font-size: 2.5rem;
            margin-bottom: 20px;
        }
        
        .component-container p {
            color: #fff;
            font-size: 1.2rem;
            margin-bottom: 30px;
        }
        
        .back-button a {
            display: inline-block;
            padding: 15px 30px;
            background-color: #272424;
            color: #ffe4ac;
            text-decoration: none;
            border-radius: 20px;
            font-weight: 600;
            transition: background-color 0.3s ease;
        }
        
        .back-button a:hover {
            background-color: #3a3636;
        }
        
        body {
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
            margin: 0;
            padding: 0;
        }
    </style>
</body>
</html>
