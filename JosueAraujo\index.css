.lightwoodkingking2-icon {
  	position: absolute;
  	top: 51px;
  	left: calc(50% - 161px);
  	width: 321.1px;
  	height: 626.1px;
  	object-fit: contain;
}
.bienvenido-al-sitio {
  	margin: 0;
  	font-weight: 300;
}
.maestro-internacional {
  	font-weight: 300;
}
.josue-araujo-sanchez {
  	font-weight: 800;
  	font-family: Outfit;
  	color: #ffe4ac;
}
.maestro-internacional-josue-ar {
  	margin: 0;
}
.bienvenido-al-sitio-container {
  	position: absolute;
  	top: 634px;
  	left: calc(50% - 463px);
  	display: inline-block;
  	width: 925px;
}
.frame-child {
  	align-self: stretch;
  	position: relative;
  	border-radius: 20px;
  	background-color: #272424;
  	height: 123px;
}
.rectangle-wrapper {
  	position: absolute;
  	top: 0px;
  	left: 0px;
  	width: 391px;
  	display: flex;
  	flex-direction: column;
  	align-items: flex-start;
  	justify-content: flex-start;
  	padding: 10px;
  	box-sizing: border-box;
}
.continuar {
  	position: absolute;
  	top: 44px;
  	left: calc(50% - 95.5px);
  	font-weight: 500;
}
.frame-group {
  	position: absolute;
  	top: 770px;
  	left: 764px;
  	width: 391px;
  	height: 143px;
  	cursor: pointer;
}
.lightwoodkingking2-parent {
  	position: absolute;
  	top: 4333px;
  	left: 10842px;
  	background: linear-gradient(180deg, #76767c, #b1b1b7);
  	width: 1920px;
  	height: 960px;
  	overflow: hidden;
}
.macbook-pro-16-1-child {
  	position: absolute;
  	top: 3772px;
  	left: 0px;
  	background: linear-gradient(180deg, #000, #666);
  	width: 1728px;
  	height: 1046px;
}
.macbook-pro-16-1-item {
  	position: absolute;
  	top: 1812px;
  	left: 0px;
  	width: 1728px;
  	height: 1324px;
  	object-fit: cover;
  	opacity: 0.5;
}
.macbook-pro-16-1-inner {
  	position: absolute;
  	top: 160px;
  	left: 0px;
  	background: linear-gradient(180deg, #fff, #dfdfdf);
  	width: 1728px;
  	height: 658px;
}
.macbook-pro-16-1-child1 {
  	position: absolute;
  	top: 0px;
  	left: 0px;
  	background-color: #000;
  	width: 1728px;
  	height: 160px;
}
.inicio {
  	position: absolute;
  	top: 60px;
  	left: 413px;
}
.inscribete {
  	position: absolute;
  	top: 60px;
  	left: 567px;
  	cursor: pointer;
}
.retos-tacticos {
  	position: absolute;
  	top: 60px;
  	left: 991px;
  	cursor: pointer;
}
.sobre-mi {
  	position: absolute;
  	top: 60px;
  	left: 786px;
  	cursor: pointer;
}
.macbook-pro-16-1-child2 {
  	position: absolute;
  	top: 41px;
  	left: 1280px;
  	border-radius: 20px;
  	background: linear-gradient(180deg, #8b8b8b, #d5d5d5);
  	width: 193px;
  	height: 78px;
}
.macbook-pro-16-1-child3 {
  	position: absolute;
  	top: 41px;
  	left: 1513px;
  	border-radius: 20px;
  	background: linear-gradient(180deg, #ffea00, #fff68f);
  	width: 193px;
  	height: 78px;
}
.registrarse {
  	position: absolute;
  	top: 60px;
  	left: 1291px;
}
.acceder {
  	position: absolute;
  	top: 60px;
  	left: 1548px;
  	color: #000;
}
.quieres-aumentar-tu-container {
  	position: absolute;
  	top: 377px;
  	left: 10px;
  	font-size: 64px;
  	font-weight: 600;
  	color: #000;
  	display: inline-block;
  	width: 1140px;
  	height: 103px;
}
.group-child {
  	position: absolute;
  	top: 0px;
  	left: 0px;
  	border-radius: 50px;
  	background: linear-gradient(90deg, #ffea00, #fff68f);
  	width: 517px;
  	height: 171px;
}
.inscribete-a-la {
  	position: absolute;
  	top: 65px;
  	left: 42px;
  	font-weight: 900;
}
.rectangle-parent {
  	position: absolute;
  	top: 583px;
  	left: 315px;
  	width: 517px;
  	height: 171px;
  	cursor: pointer;
  	color: #000;
}
.macbook-pro-16-1-child4 {
  	position: absolute;
  	top: 1850px;
  	left: 0px;
  	background: linear-gradient(90deg, #968506, #dac104);
  	width: 1728px;
  	height: 165px;
}
.elige-tu-membresia {
  	position: absolute;
  	top: 1854px;
  	left: 272px;
  	font-size: 128px;
  	font-weight: 800;
}
.group-item {
  	position: absolute;
  	top: 0px;
  	left: 41px;
  	border-radius: 50px;
  	background-color: #fff;
  	width: 500px;
  	height: 800px;
}
.group-inner {
  	position: absolute;
  	top: 656px;
  	left: 87px;
  	border-radius: 10px;
  	background-color: #968506;
  	width: 408px;
  	height: 106px;
}
.elegir-este-plan {
  	position: absolute;
  	top: 691px;
  	left: 0px;
  	font-weight: 900;
  	text-align: center;
  	display: inline-block;
  	width: 583px;
  	height: 32px;
}
.accede-a-la {
  	margin: 0;
  	font-size: 24px;
  	font-weight: 300;
  	color: #575757;
}
.mensual-accede-a-container {
  	position: absolute;
  	top: 51px;
  	left: 71px;
  	letter-spacing: 0.01em;
  	line-height: 30px;
  	display: inline-block;
  	width: 401px;
  	font-size: 64px;
  	color: #000;
}
.clases-interactivas-en {
  	margin-bottom: 0px;
}
.clases-interactivas-en-vivo-5 {
  	margin: 0;
  	font-family: inherit;
  	font-size: inherit;
  	padding-left: 32px;
}
.clases-interactivas-en-container {
  	position: absolute;
  	top: 346px;
  	left: 49px;
  	font-weight: 300;
  	color: #686666;
  	display: inline-block;
  	width: 492px;
}
.group-wrapper {
  	position: absolute;
  	top: 0px;
  	left: 0px;
  	width: 583px;
  	height: 800px;
}
.group-div {
  	position: absolute;
  	top: 2079px;
  	left: 0px;
  	width: 583px;
  	height: 800px;
  	text-align: left;
  	font-size: 24px;
}
.group-child1 {
  	position: absolute;
  	top: 0px;
  	left: 0px;
  	border-radius: 50px;
  	background-color: #fff;
  	width: 500px;
  	height: 800px;
}
.macbook-pro-16-1-inner1 {
  	position: absolute;
  	top: 2081px;
  	left: 603px;
  	width: 500px;
  	height: 800px;
}
.macbook-pro-16-1-child5 {
  	position: absolute;
  	top: 2737px;
  	left: 642px;
  	border-radius: 10px;
  	background-color: #968506;
  	width: 408px;
  	height: 106px;
}
.elegir-este-plan1 {
  	position: absolute;
  	top: 2772px;
  	left: 555px;
  	font-size: 24px;
  	font-weight: 900;
  	display: inline-block;
  	width: 583px;
  	height: 32px;
}
.trimestral-accede-a-container {
  	position: absolute;
  	top: 2132px;
  	left: 626px;
  	letter-spacing: 0.01em;
  	line-height: 30px;
  	text-align: left;
  	display: inline-block;
  	width: 401px;
  	font-size: 64px;
  	color: #000;
}
.clases-interactivas-en-container1 {
  	position: absolute;
  	top: 2427px;
  	left: 604px;
  	font-size: 24px;
  	font-weight: 300;
  	color: #686666;
  	text-align: left;
  	display: inline-block;
  	width: 492px;
}
.macbook-pro-16-1-inner2 {
  	position: absolute;
  	top: 2079px;
  	left: 1123px;
  	width: 583px;
  	height: 800px;
  	text-align: left;
  	font-size: 24px;
}
.group-child4 {
  	position: absolute;
  	top: 1001px;
  	left: 1728px;
  	background: linear-gradient(180deg, #0aa1ff, #066199);
  	width: 1728px;
  	height: 1001px;
  	transform:  rotate(180deg);
  	transform-origin: 0 0;
}
.ellipse-div {
  	position: absolute;
  	top: 156px;
  	left: 35px;
  	border-radius: 50%;
  	background: linear-gradient(180deg, #fff, #999);
  	width: 150px;
  	height: 150px;
}
.image-7-icon {
  	position: absolute;
  	top: 0px;
  	left: 0px;
  	width: 100px;
  	height: 100px;
  	object-fit: cover;
}
.clases-interactivas {
  	margin: 0;
  	font-weight: 800;
}
.aprende-con-el {
  	margin: 0;
  	font-size: 32px;
  	font-weight: 100;
}
.clases-interactivas-aprende-container {
  	width: 100%;
  	position: absolute;
  	top: 184px;
  	left: 0px;
  	display: inline-block;
}
.image-7-parent {
  	position: absolute;
  	top: 181px;
  	left: 60px;
  	width: 588px;
  	display: grid;
  	grid-template-rows: 10;
  	grid-template-columns: 10;
  	justify-content: start;
  	align-content: start;
  	column-gap: 50px;
  	color: #000;
}
.group-child5 {
  	position: absolute;
  	top: 614px;
  	left: 34px;
  	border-radius: 50%;
  	background: linear-gradient(180deg, #fff, #999);
  	width: 150px;
  	height: 150px;
}
.group-child6 {
  	position: absolute;
  	top: 150px;
  	left: 813px;
  	border-radius: 50%;
  	background: linear-gradient(180deg, #fff, #999);
  	width: 150px;
  	height: 150px;
}
.frame-item {
  	position: absolute;
  	top: 99px;
  	left: 0px;
  	width: 100px;
  	height: 100px;
  	object-fit: cover;
}
.material-de-estudio-container {
  	position: absolute;
  	top: 0px;
  	left: 0px;
  	display: inline-block;
  	width: 588px;
}
.material-de-estudio-accede-a-r-wrapper {
  	position: absolute;
  	top: 259px;
  	left: 0px;
  	width: 588px;
  	height: 128px;
}
.group-parent {
  	position: absolute;
  	top: 545px;
  	left: 60px;
  	width: 588px;
  	display: grid;
  	grid-template-rows: 10;
  	grid-template-columns: 10;
  	justify-content: start;
  	align-content: start;
  	column-gap: 46px;
  	padding: 99px 0px;
  	box-sizing: border-box;
}
.group-child7 {
  	position: absolute;
  	top: 0px;
  	left: 0px;
  	border-radius: 50%;
  	background: linear-gradient(180deg, #fff, #999);
  	width: 150px;
  	height: 150px;
}
.ejercicios-y-retos-container {
  	position: absolute;
  	top: 176px;
  	left: 0px;
  	display: inline-block;
  	width: 588px;
}
.group-container {
  	position: absolute;
  	top: 26px;
  	left: 32px;
  	width: 100px;
  	height: 100px;
}
.ellipse-parent {
  	position: absolute;
  	top: 594px;
  	left: 837px;
  	width: 150px;
  	height: 150px;
}
.revisin-de-tus-container {
  	width: 100%;
  	position: absolute;
  	top: 164px;
  	left: 0px;
  	display: inline-block;
}
.frame-div {
  	position: absolute;
  	top: 175px;
  	left: 838px;
  	width: 588px;
  	display: grid;
  	grid-template-rows: 10;
  	grid-template-columns: 10;
  	justify-content: start;
  	align-content: start;
  	column-gap: 50px;
}
.rectangle-parent1 {
  	position: absolute;
  	top: 818px;
  	left: 0px;
  	width: 1728px;
  	height: 1017px;
  	text-align: left;
  	font-size: 40px;
}
.blackkingking2-icon {
  	position: absolute;
  	top: 84px;
  	left: 1013px;
  	width: 477px;
  	height: 1033px;
  	object-fit: cover;
}
.div {
  	position: absolute;
  	top: 0px;
  	left: 0px;
  	font-weight: 900;
}
.wrapper {
  	position: absolute;
  	top: 0px;
  	left: 0px;
  	width: 172px;
  	height: 121px;
}
.macbook-pro-16-1-inner3 {
  	position: absolute;
  	top: 2293px;
  	left: 64px;
  	width: 172px;
  	height: 121px;
  	text-align: left;
  	font-size: 96px;
  	color: #968506;
}
.macbook-pro-16-1-inner4 {
  	position: absolute;
  	top: 2295px;
  	left: 622px;
  	width: 172px;
  	height: 121px;
  	text-align: left;
  	font-size: 96px;
  	color: #968506;
}
.macbook-pro-16-1-inner5 {
  	position: absolute;
  	top: 2295px;
  	left: 1194px;
  	width: 172px;
  	height: 121px;
  	text-align: left;
  	font-size: 96px;
  	color: #968506;
}
.whitequeenqueen-icon {
  	position: absolute;
  	top: 811px;
  	left: 1425px;
  	width: 272px;
  	height: 1043px;
  	object-fit: cover;
  	opacity: 0.5;
}
.group-child8 {
  	position: absolute;
  	top: 0px;
  	left: 0px;
  	background-color: #000;
  	width: 1728px;
  	height: 576px;
  	opacity: 0.8;
}
.line-icon {
  	position: absolute;
  	top: 133px;
  	left: 71px;
  	max-height: 100%;
  	width: 1453px;
}
.necesitas-ayuda {
  	position: absolute;
  	top: 157px;
  	left: 639px;
  	font-size: 40px;
}
.ubicacin {
  	position: absolute;
  	top: 177px;
  	left: 158px;
}
.correo-electrnico {
  	position: absolute;
  	top: 309px;
  	left: 158px;
}
.santo-domingo-rep {
  	position: absolute;
  	top: 227px;
  	left: 158px;
  	font-weight: 100;
}
.ipsumlorengmailcom {
  	position: absolute;
  	top: 359px;
  	left: 158px;
  	text-decoration: underline;
  	font-weight: 100;
  	color: inherit;
}
.faqs {
  	position: absolute;
  	top: 227px;
  	left: 767px;
  	font-weight: 100;
}
.contacto {
  	position: absolute;
  	top: 287px;
  	left: 744px;
  	font-weight: 100;
}
.medios-de-pago {
  	position: absolute;
  	top: 163px;
  	left: 1206px;
  	font-size: 40px;
}
.image-11-icon {
  	position: absolute;
  	top: 359px;
  	left: 671px;
  	width: 80px;
  	height: 80px;
  	object-fit: cover;
}
.image-12-icon {
  	position: absolute;
  	top: 359px;
  	left: 769px;
  	width: 80px;
  	height: 80px;
  	object-fit: cover;
}
.image-13-icon {
  	position: absolute;
  	top: 359px;
  	left: 867px;
  	width: 80px;
  	height: 80px;
  	object-fit: cover;
}
.paypal-2014-logo-1-icon {
  	position: absolute;
  	top: 252px;
  	left: 1235px;
  	width: 100px;
  	height: 99px;
  	object-fit: cover;
}
.mask-group-icon {
  	position: absolute;
  	top: 0px;
  	left: 71px;
  	width: 143.5px;
  	height: 143.5px;
  	object-fit: cover;
}
.llega-airtm-la-unica-cuenta-qu-icon {
  	position: absolute;
  	top: 238px;
  	left: 1383px;
  	border-radius: 50px;
  	width: 100px;
  	height: 100px;
  	object-fit: cover;
}
.rectangle-parent2 {
  	position: absolute;
  	top: 0px;
  	left: 0px;
  	width: 1728px;
  	height: 576px;
}
.macbook-pro-16-1-inner6 {
  	position: absolute;
  	top: 4818px;
  	left: 0px;
  	width: 1728px;
  	height: 576px;
}
.mask-group-icon1 {
  	position: absolute;
  	top: 0px;
  	left: 60px;
  	width: 167px;
  	height: 167px;
  	object-fit: cover;
}
.hola-soy {
  	font-weight: 300;
  	font-family: Outfit;
}
.josue-araujo-sanchez1 {
  	font-family: Outfit;
}
.hola-soy-josue-container {
  	position: absolute;
  	top: 3252px;
  	left: 88px;
  	font-size: 36px;
  	color: #000;
  	text-align: left;
  	display: inline-block;
  	width: 794px;
}
.ejercicio-del-dia {
  	position: absolute;
  	top: 3863px;
  	left: 122px;
  	font-size: 96px;
  	text-align: left;
  	display: inline-block;
  	width: 794px;
}
.juegan-blancas-mate-container {
  	position: absolute;
  	top: 4262px;
  	left: 865px;
  	font-size: 96px;
  	display: inline-block;
  	width: 794px;
}
.fdm2vdwxmaitxwx-1-icon {
  	position: absolute;
  	top: 3112px;
  	left: 930px;
  	border-radius: 100px;
  	width: 840px;
  	height: 686px;
  	object-fit: cover;
}
.image-14-icon {
  	position: absolute;
  	top: 4015px;
  	left: 71px;
  	width: 777px;
  	height: 772px;
  	object-fit: cover;
}
.macbook-pro-16-1 {
  	position: absolute;
  	top: 5374px;
  	left: 8273px;
  	background: linear-gradient(180deg, #fff, #fff);
  	width: 1728px;
  	height: 5394px;
  	overflow: hidden;
  	font-size: 32px;
}
.frame-parent {
  	width: 100%;
  	position: relative;
  	background-color: #fff;
  	height: 15101px;
  	overflow: hidden;
  	text-align: center;
  	font-size: 42.9px;
  	color: #fff;
  	font-family: Outfit;
}